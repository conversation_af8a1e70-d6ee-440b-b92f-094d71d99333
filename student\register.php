<?php
session_start();
require '../core/dbcon.ini';
require 'query/students.qry';
require 'classes/os_browser.php';

date_default_timezone_set('Asia/Manila');
$current_date = date("Y-m-d");

// Initialize class
$students = new STUDENTS();

// Get device info
$ip_address = getenv("REMOTE_ADDR");
$user_os = getOS();
$user_browser = getBrowser();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db1->beginTransaction();

        $student_id = $_POST['student_id'];
        $os = $_POST['os'];
        $browser = $_POST['browser'];

        // Check if student exists in cdas_student table
        if (!$students->check_student_exists($db1, $student_id)) {
            throw new Exception("Student ID not found in the system. Please contact the administrator.");
        }

        // Check if student ID is already registered with a device
        if ($students->check_student_device_registered($db1, $student_id)) {
            throw new Exception("Student ID is already registered with another device. Only one device per student is allowed.");
        }

        // Register device for this student
        if ($students->register_device($db1, $student_id, $ip_address, $os, $browser)) {
            $db1->commit();
            $message = "Device successfully registered for Student ID: $student_id. IP Address: $ip_address";
            echo "<script>
                alert('$message');
                window.location.href = 'attendance.php';
            </script>";
            exit();
        } else {
            throw new Exception("Failed to register device. Please try again.");
        }

    } catch(Exception $e) {
        $db1->rollBack();
        echo "<script>
            alert('". $e->getMessage() ."'); 
            window.history.back();
        </script>";
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title>Don Bosco College - Device Registration</title>
    <link rel="stylesheet" href="css/register.css">
</head>
<body>
    <div class="register-container">
        <h1>Device Registration</h1>
        <p>Register your device to access the attendance system</p>

        <div class="info-box">
            <h3>Device Information</h3>
            <p><strong>IP Address:</strong> <?php echo $ip_address; ?></p>
            <p><strong>Operating System:</strong> <?php echo $user_os; ?></p>
            <p><strong>Browser:</strong> <?php echo $user_browser; ?></p>
        </div>

        <form method="post">
            <div class="form-group">
                <label for="student_id">
                    Student ID
                    <span class="info-icon" data-tooltip="Your Student ID is an 8-digit number found on your ID card">ⓘ</span>
                </label>
                <input type="text" id="student_id" name="student_id" placeholder="Enter your 8-digit Student ID" required>
                <div class="input-guide">
                    Enter your 8-digit Student ID (e.g., 12345678).
                    <span class="view-sample-link" onclick="showSampleTooltip(this)">view sample</span>
                </div>
            </div>

            <input type="hidden" name="os" value="<?php echo $user_os; ?>">
            <input type="hidden" name="browser" value="<?php echo $user_browser; ?>">

            <button type="submit">
                Register Device
            </button>
        </form>

        <p class="footer-text">
            Your Student ID must exist in the system. Contact the administrator if you encounter issues.
        </p>

        <a href="attendance.php" class="back-link">
            ← Back to Attendance
        </a>
    </div>

    <!-- Sample Tooltip -->
    <div id="sampleTooltip" class="sample-tooltip">
        <div class="tooltip-content">
            <span class="tooltip-close" onclick="hideSampleTooltip()">&times;</span>
            <img src="assets/sample-id.jpg" alt="Student ID Sample" class="tooltip-image">
            <p class="tooltip-text">Your Student ID is located on your ID card as shown above</p>
        </div>
    </div>

    <script>
        function showSampleTooltip(element) {
            const tooltip = document.getElementById('sampleTooltip');
            tooltip.style.display = 'block';

            // Position tooltip near the clicked element
            const rect = element.getBoundingClientRect();
            tooltip.style.top = (rect.bottom + window.scrollY + 10) + 'px';
            tooltip.style.left = (rect.left + window.scrollX) + 'px';
        }

        function hideSampleTooltip() {
            document.getElementById('sampleTooltip').style.display = 'none';
        }

        // Close tooltip when clicking outside
        document.addEventListener('click', function(event) {
            const tooltip = document.getElementById('sampleTooltip');
            const viewSampleLink = event.target.closest('.view-sample-link');

            if (!tooltip.contains(event.target) && !viewSampleLink) {
                tooltip.style.display = 'none';
            }
        });
    </script>
</body>
</html>
