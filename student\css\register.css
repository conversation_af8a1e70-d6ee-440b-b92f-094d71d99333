
:root {
    /* <PERSON> Color Scheme */
    --primary-color: #004AAD; /* Deep Royal Blue */
    --primary-hover: #0056d3;
    --accent-color: #FFC300; /* Gold/Yellow */
    --accent-hover: #FFD700;
    --success-color: #4CAF50; /* Green */
    --error-color: #FF4B4B; /* Red */
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --bg-primary: #F8F9FA; /* Light background */
    --card-bg: #FFFFFF; /* White background */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow-soft: 0 4px 20px rgba(0, 74, 173, 0.1);
    --shadow-medium: 0 8px 30px rgba(0, 74, 173, 0.15);
}

body {
    font-family: 'Plus Jakarta Sans', sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, var(--bg-primary) 0%, #e8f0fe 100%);
    color: var(--text-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.register-container {
    background: var(--card-bg);
    border-radius: 24px;
    box-shadow: var(--shadow-medium);
    width: 90%;
    max-width: 400px;
    padding: 40px 32px;
    text-align: center;
    margin: 20px;
    border: 1px solid rgba(0, 74, 173, 0.1);
}

h1 {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 16px;
    color: var(--primary-color);
    letter-spacing: -0.02em;
}

.register-container p {
    color: var(--text-secondary);
    margin-bottom: 30px;
    font-size: 15px;
    font-weight: 500;
}

.form-group {
    margin-bottom: 24px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    transition: var(--transition);
    background: var(--card-bg);
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
}

.info-box {
    background: rgba(255, 215, 0, 0.15);
    border: 2px solid var(--accent-color);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: left;
}

.info-box h3 {
    color: var(--primary-color);
    margin: 0 0 12px 0;
    font-size: 16px;
}

.info-box p {
    margin: 8px 0;
    font-size: 14px;
    color: var(--text-secondary);
}

/* Button Styling */
button {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: 2px solid var(--accent-color);
    border-radius: 16px;
    color: white;
    padding: 20px 36px;
    margin: 12px auto;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
    width: 100%;
    max-width: none;
    margin-top: 20px;
    display: block;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: inherit;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
    transform: translateY(-100%);
    transition: var(--transition);
}

button:hover::before {
    transform: translateY(0);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 74, 173, 0.3);
    border-color: var(--accent-hover);
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
    color: #9CA3AF;
    cursor: not-allowed;
    box-shadow: none;
}

/* Back Link Styling */
.back-link {
    display: inline-block;
    margin-top: 16px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.back-link:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.footer-text {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 20px;
}

/* Info Icon Styles */
.info-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-align: center;
    font-size: 12px;
    line-height: 16px;
    cursor: help;
    margin-left: 5px;
    position: relative;
}

.info-icon:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 100;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.info-icon:hover::before {
    content: '';
    position: absolute;
    bottom: 115%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--text-primary);
    z-index: 100;
}

/* Input Guide Styles */
.input-guide {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 6px;
    line-height: 1.4;
}

.view-sample-link {
    color: var(--primary-color);
    text-decoration: underline;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.view-sample-link:hover {
    color: var(--primary-hover);
}

/* Sample Tooltip Styles */
.sample-tooltip {
    display: none;
    position: absolute;
    z-index: 1000;
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 74, 173, 0.1);
    padding: 15px;
    max-width: 300px;
    animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tooltip-content {
    position: relative;
}

.tooltip-close {
    position: absolute;
    top: -5px;
    right: -5px;
    color: var(--text-secondary);
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    background: var(--card-bg);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tooltip-close:hover {
    color: var(--primary-color);
}

.tooltip-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid var(--accent-color);
}

.tooltip-text {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
    text-align: center;
}

/* Mobile Responsive Design */
@media (max-width: 600px) {
    .register-container {
        padding: 32px 24px;
        margin: 15px;
        max-width: 350px;
    }

    h1 {
        font-size: 24px;
        margin-bottom: 12px;
    }

    .register-container p {
        font-size: 14px;
        margin-bottom: 24px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group input {
        padding: 14px;
        font-size: 15px;
    }

    .info-box {
        padding: 16px;
        margin-bottom: 24px;
    }

    .info-box h3 {
        font-size: 15px;
    }

    .info-box p {
        font-size: 13px;
    }

    button {
        padding: 16px 24px;
        font-size: 15px;
        margin-top: 16px;
    }

    .footer-text {
        font-size: 11px;
        margin-top: 16px;
    }

    .input-guide {
        font-size: 11px;
    }

    .sample-tooltip {
        max-width: 250px;
        padding: 12px;
    }

    .tooltip-text {
        font-size: 11px;
    }
}

@media (max-width: 400px) {
    .register-container {
        padding: 24px 16px;
        margin: 10px;
        max-width: 320px;
    }

    h1 {
        font-size: 22px;
    }

    .form-group input {
        padding: 12px;
        font-size: 14px;
    }

    .info-box {
        padding: 14px;
    }

    button {
        padding: 14px 20px;
        font-size: 14px;
    }

    .input-guide {
        font-size: 10px;
    }

    .sample-tooltip {
        max-width: 200px;
        padding: 10px;
    }

    .tooltip-text {
        font-size: 10px;
    }

    .info-icon {
        width: 14px;
        height: 14px;
        font-size: 10px;
        line-height: 14px;
    }
}
