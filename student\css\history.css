
:root {
    /* <PERSON> Color Scheme */
    --primary-color: #004AAD; /* Deep Royal Blue */
    --primary-hover: #0056d3;
    --accent-color: #FFC300; /* Gold/Yellow */
    --accent-hover: #FFD700;
    --success-color: #4CAF50; /* Green */
    --error-color: #FF4B4B; /* Red */
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --bg-primary: #F8F9FA; /* Light background */
    --card-bg: #FFFFFF; /* White background */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow-soft: 0 4px 20px rgba(0, 74, 173, 0.1);
    --shadow-medium: 0 8px 30px rgba(0, 74, 173, 0.15);
}

body {
    font-family: 'Plus Jakarta Sans', sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, var(--bg-primary) 0%, #e8f0fe 100%);
    color: var(--text-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.container {
    background: var(--card-bg);
    border-radius: 20px;
    box-shadow: var(--shadow-medium);
    width: 90%;
    max-width: 420px;
    padding: 24px;
    text-align: center;
    transition: var(--transition);
    margin: 15px;
    min-height: auto;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 74, 173, 0.1);
}

/* Don Bosco College Header Design */
.container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    opacity: 0.5;
    border-radius: 0 0 50% 50%;
}

.header {
    padding: 20px 0;
    text-align: center;
    position: relative;
    z-index: 1;
    margin-bottom: 20px;
}

h1 {
    font-size: 22px;
    font-weight: 700;
    margin: 12px 0 0 0;
    color: var(--primary-color);
    letter-spacing: -0.02em;
    z-index: 2;
    position: relative;
}
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 10px 0;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

th, td {
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid rgba(0, 74, 173, 0.1);
}

th {
    background: rgba(0, 74, 173, 0.05);
    font-weight: 600;
    color: var(--primary-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

td {
    color: var(--text-secondary);
    font-size: 12px;
    transition: var(--transition);
}

tr:hover td {
    background: rgba(0, 74, 173, 0.02);
    color: var(--text-primary);
}

tbody tr:last-child td {
    border-bottom: none;
}
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    gap: 6px;
    flex-wrap: wrap;
}

.pagination a {
    color: var(--primary-color);
    padding: 8px 12px;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 500;
    transition: var(--transition);
    background: rgba(0, 74, 173, 0.05);
    min-width: 36px;
    text-align: center;
    font-size: 13px;
    border: 1px solid rgba(0, 74, 173, 0.1);
}

.pagination a.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 74, 173, 0.2);
    border-color: var(--primary-color);
}

.pagination a:hover:not(.active) {
    background: rgba(0, 74, 173, 0.1);
    transform: translateY(-1px);
    border-color: var(--primary-color);
}
.no-data {
    text-align: center;
    padding: 32px 16px;
    color: var(--text-secondary);
    font-size: 14px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 16px;
    margin: 16px 0;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.back-btn {
    display: block;
    margin: 16px auto 0;
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 14px;
    transition: var(--transition);
    text-align: center;
    max-width: 180px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 195, 0, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 13px;
    border: 2px solid var(--primary-color);
}

.back-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 195, 0, 0.4);
    background: linear-gradient(135deg, var(--accent-hover) 0%, var(--accent-color) 100%);
}

/* Mobile Responsive Design */
@media (max-width: 600px) {
    .container {
        padding: 20px 12px;
        margin: 12px;
        min-height: auto;
    }

    .container::after {
        height: 70px;
    }

    .profile {
        width: 100px;
        height: 100px;
        margin-bottom: 14px;
        padding: 3px;
    }

    h1 {
        font-size: 18px;
        margin-bottom: 10px;
    }

    table {
        margin: 8px 0;
        border-radius: 12px;
    }

    th, td {
        padding: 8px 6px;
        font-size: 11px;
    }

    th {
        font-size: 10px;
    }

    .pagination {
        padding: 12px 0;
        gap: 4px;
    }

    .pagination a {
        padding: 6px 10px;
        font-size: 12px;
        min-width: 32px;
    }

    .back-btn {
        max-width: 160px;
        padding: 10px 20px;
        font-size: 12px;
        margin: 12px auto 0;
    }
}

@media (max-width: 400px) {
    .container {
        padding: 16px 10px;
        margin: 10px;
    }

    .container::after {
        height: 50px;
    }

    .profile {
        width: 80px;
        height: 80px;
        margin-bottom: 12px;
        padding: 2px;
    }

    h1 {
        font-size: 16px;
    }

    table {
        margin: 6px 0;
        border-radius: 10px;
    }

    th, td {
        padding: 6px 4px;
        font-size: 10px;
    }

    th {
        font-size: 9px;
    }

    .pagination {
        padding: 10px 0;
        gap: 2px;
    }

    .pagination a {
        padding: 5px 8px;
        font-size: 11px;
        min-width: 28px;
    }

    .back-btn {
        max-width: 140px;
        padding: 8px 16px;
        font-size: 11px;
        margin: 10px auto 0;
    }
}